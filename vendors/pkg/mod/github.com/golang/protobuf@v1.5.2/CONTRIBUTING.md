# Contributing to Go Protocol Buffers

Go protocol buffers is an open source project and accepts contributions.

This project is the first major version of Go protobufs,
while the next major revision of this project is located at
[protocolbuffers/protobuf-go](https://github.com/protocolbuffers/protobuf-go).
Most new development effort is focused on the latter project,
and changes to this project is primarily reserved for bug fixes.


## Contributor License Agreement

Contributions to this project must be accompanied by a Contributor License
Agreement. You (or your employer) retain the copyright to your contribution,
this simply gives us permission to use and redistribute your contributions as
part of the project. Head over to <https://cla.developers.google.com/> to see
your current agreements on file or to sign a new one.

You generally only need to submit a CLA once, so if you've already submitted one
(even if it was for a different project), you probably don't need to do it
again.


## Code reviews

All submissions, including submissions by project members, require review. We
use GitHub pull requests for this purpose. Consult
[GitHub Help](https://help.github.com/articles/about-pull-requests/) for more
information on using pull requests.
