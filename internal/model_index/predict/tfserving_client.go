package predict

import (
	"context"
	"fmt"
	"rtb_model_server/internal/zaplog"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
)

// TFServingClient TensorFlow Serving gRPC客户端
type TFServingClient struct {
	conn       *grpc.ClientConn
	serverAddr string
	modelName  string
}

// NewTFServingClient 创建TensorFlow Serving客户端
func NewTFServingClient(serverAddr, modelName string) (*TFServingClient, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	zaplog.Logger.Info("Connecting to TensorFlow Serving", 
		zap.String("server", serverAddr), 
		zap.String("model", modelName))

	conn, err := grpc.DialContext(ctx, serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		zaplog.Logger.Error("Failed to connect to TensorFlow Serving", 
			zap.String("server", serverAddr), 
			zap.Error(err))
		return nil, fmt.Errorf("grpc connection failed: %w", err)
	}

	zaplog.Logger.Info("TensorFlow Serving connection established", 
		zap.String("server", serverAddr))

	return &TFServingClient{
		conn:       conn,
		serverAddr: serverAddr,
		modelName:  modelName,
	}, nil
}

// Close 关闭连接
func (c *TFServingClient) Close() {
	if c.conn != nil {
		c.conn.Close()
		zaplog.Logger.Info("TensorFlow Serving connection closed")
	}
}

// TestConnection 测试连接状态
func (c *TFServingClient) TestConnection() error {
	state := c.conn.GetState()
	if state != connectivity.Ready {
		return fmt.Errorf("connection not ready, state: %v", state)
	}
	return nil
}

// Predict 执行预测
func (c *TFServingClient) Predict(ctx context.Context, features map[string]interface{}) (map[string]interface{}, error) {
	if err := c.TestConnection(); err != nil {
		return nil, fmt.Errorf("connection check failed: %w", err)
	}

	request := &PredictRequest{
		ModelSpec: &ModelSpec{
			Name:          c.modelName,
			SignatureName: "serving_default",
		},
		Inputs: make(map[string]*TensorProto),
	}

	for name, value := range features {
		tensor, err := c.convertToTensor(value)
		if err != nil {
			zaplog.Logger.Error("Failed to convert feature to tensor", 
				zap.String("feature", name), 
				zap.Error(err))
			return nil, fmt.Errorf("feature conversion failed for %s: %w", name, err)
		}
		request.Inputs[name] = tensor
	}

	client := NewPredictionServiceClient(c.conn)
	
	zaplog.Logger.Debug("Sending prediction request", 
		zap.String("model", c.modelName),
		zap.Int("input_count", len(request.Inputs)))

	response, err := client.Predict(ctx, request)
	if err != nil {
		zaplog.Logger.Error("Prediction request failed", 
			zap.String("model", c.modelName), 
			zap.Error(err))
		return nil, fmt.Errorf("prediction failed: %w", err)
	}

	zaplog.Logger.Debug("Prediction request completed", 
		zap.String("model", c.modelName),
		zap.Int("output_count", len(response.Outputs)))

	result := make(map[string]interface{})
	for name, tensor := range response.Outputs {
		value, err := c.convertFromTensor(tensor)
		if err != nil {
			zaplog.Logger.Error("Failed to convert tensor to value", 
				zap.String("output", name), 
				zap.Error(err))
			continue
		}
		result[name] = value
	}

	return result, nil
}

// convertToTensor 将值转换为张量
func (c *TFServingClient) convertToTensor(value interface{}) (*TensorProto, error) {
	tensor := &TensorProto{}

	switch v := value.(type) {
	case []int64:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.Int64Val = v
	case []float32:
		tensor.Dtype = DataType_DT_FLOAT
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.FloatVal = v
	case []float64:
		tensor.Dtype = DataType_DT_DOUBLE
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.DoubleVal = v
	case int64:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.Int64Val = []int64{v}
	case float32:
		tensor.Dtype = DataType_DT_FLOAT
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.FloatVal = []float32{v}
	case float64:
		tensor.Dtype = DataType_DT_DOUBLE
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.DoubleVal = []float64{v}
	default:
		return nil, fmt.Errorf("unsupported type: %T", value)
	}

	return tensor, nil
}

// convertFromTensor 将张量转换为值
func (c *TFServingClient) convertFromTensor(tensor *TensorProto) (interface{}, error) {
	switch tensor.Dtype {
	case DataType_DT_INT64:
		return tensor.Int64Val, nil
	case DataType_DT_FLOAT:
		return tensor.FloatVal, nil
	case DataType_DT_DOUBLE:
		return tensor.DoubleVal, nil
	default:
		return nil, fmt.Errorf("unsupported tensor type: %v", tensor.Dtype)
	}
}
