// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.29.3
// source: predict.proto

package predict

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DataType enum from tensorflow/core/framework/types.proto
type DataType int32

const (
	DataType_DT_INVALID DataType = 0
	DataType_DT_FLOAT   DataType = 1
	DataType_DT_DOUBLE  DataType = 2
	DataType_DT_INT32   DataType = 3
	DataType_DT_UINT8   DataType = 4
	DataType_DT_INT16   DataType = 5
	DataType_DT_INT8    DataType = 6
	DataType_DT_STRING  DataType = 7
	DataType_DT_INT64   DataType = 9
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0: "DT_INVALID",
		1: "DT_FLOAT",
		2: "DT_DOUBLE",
		3: "DT_INT32",
		4: "DT_UINT8",
		5: "DT_INT16",
		6: "DT_INT8",
		7: "DT_STRING",
		9: "DT_INT64",
	}
	DataType_value = map[string]int32{
		"DT_INVALID": 0,
		"DT_FLOAT":   1,
		"DT_DOUBLE":  2,
		"DT_INT32":   3,
		"DT_UINT8":   4,
		"DT_INT16":   5,
		"DT_INT8":    6,
		"DT_STRING":  7,
		"DT_INT64":   9,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_predict_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_predict_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{0}
}

// Model specification from tensorflow_serving/apis/model.proto
type ModelSpec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SignatureName string `protobuf:"bytes,2,opt,name=signature_name,json=signatureName,proto3" json:"signature_name,omitempty"`
	Version       int64  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ModelSpec) Reset() {
	*x = ModelSpec{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModelSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSpec) ProtoMessage() {}

func (x *ModelSpec) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSpec.ProtoReflect.Descriptor instead.
func (*ModelSpec) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{0}
}

func (x *ModelSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelSpec) GetSignatureName() string {
	if x != nil {
		return x.SignatureName
	}
	return ""
}

func (x *ModelSpec) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Tensor shape from tensorflow/core/framework/tensor_shape.proto
type TensorShapeProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dim []*TensorShapeProto_Dim `protobuf:"bytes,2,rep,name=dim,proto3" json:"dim,omitempty"`
}

func (x *TensorShapeProto) Reset() {
	*x = TensorShapeProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorShapeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto) ProtoMessage() {}

func (x *TensorShapeProto) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto.ProtoReflect.Descriptor instead.
func (*TensorShapeProto) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{1}
}

func (x *TensorShapeProto) GetDim() []*TensorShapeProto_Dim {
	if x != nil {
		return x.Dim
	}
	return nil
}

// Tensor from tensorflow/core/framework/tensor.proto
type TensorProto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dtype       DataType          `protobuf:"varint,1,opt,name=dtype,proto3,enum=tensorflow.serving.DataType" json:"dtype,omitempty"`
	TensorShape *TensorShapeProto `protobuf:"bytes,2,opt,name=tensor_shape,json=tensorShape,proto3" json:"tensor_shape,omitempty"`
	FloatVal    []float32         `protobuf:"fixed32,5,rep,packed,name=float_val,json=floatVal,proto3" json:"float_val,omitempty"`
	DoubleVal   []float64         `protobuf:"fixed64,6,rep,packed,name=double_val,json=doubleVal,proto3" json:"double_val,omitempty"`
	Int64Val    []int64           `protobuf:"varint,7,rep,packed,name=int64_val,json=int64Val,proto3" json:"int64_val,omitempty"`
}

func (x *TensorProto) Reset() {
	*x = TensorProto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorProto) ProtoMessage() {}

func (x *TensorProto) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorProto.ProtoReflect.Descriptor instead.
func (*TensorProto) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{2}
}

func (x *TensorProto) GetDtype() DataType {
	if x != nil {
		return x.Dtype
	}
	return DataType_DT_INVALID
}

func (x *TensorProto) GetTensorShape() *TensorShapeProto {
	if x != nil {
		return x.TensorShape
	}
	return nil
}

func (x *TensorProto) GetFloatVal() []float32 {
	if x != nil {
		return x.FloatVal
	}
	return nil
}

func (x *TensorProto) GetDoubleVal() []float64 {
	if x != nil {
		return x.DoubleVal
	}
	return nil
}

func (x *TensorProto) GetInt64Val() []int64 {
	if x != nil {
		return x.Int64Val
	}
	return nil
}

// Predict request from tensorflow_serving/apis/predict.proto
type PredictRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelSpec *ModelSpec              `protobuf:"bytes,1,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
	Inputs    map[string]*TensorProto `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *PredictRequest) Reset() {
	*x = PredictRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictRequest) ProtoMessage() {}

func (x *PredictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictRequest.ProtoReflect.Descriptor instead.
func (*PredictRequest) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{3}
}

func (x *PredictRequest) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

func (x *PredictRequest) GetInputs() map[string]*TensorProto {
	if x != nil {
		return x.Inputs
	}
	return nil
}

// Predict response from tensorflow_serving/apis/predict.proto
type PredictResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Outputs   map[string]*TensorProto `protobuf:"bytes,1,rep,name=outputs,proto3" json:"outputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	ModelSpec *ModelSpec              `protobuf:"bytes,2,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
}

func (x *PredictResponse) Reset() {
	*x = PredictResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PredictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictResponse) ProtoMessage() {}

func (x *PredictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictResponse.ProtoReflect.Descriptor instead.
func (*PredictResponse) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{4}
}

func (x *PredictResponse) GetOutputs() map[string]*TensorProto {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *PredictResponse) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

type TensorShapeProto_Dim struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64  `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *TensorShapeProto_Dim) Reset() {
	*x = TensorShapeProto_Dim{}
	if protoimpl.UnsafeEnabled {
		mi := &file_predict_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TensorShapeProto_Dim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto_Dim) ProtoMessage() {}

func (x *TensorShapeProto_Dim) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto_Dim.ProtoReflect.Descriptor instead.
func (*TensorShapeProto_Dim) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{1, 0}
}

func (x *TensorShapeProto_Dim) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TensorShapeProto_Dim) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_predict_proto protoreflect.FileDescriptor

var file_predict_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x12, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x22, 0x60, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x10, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53,
	0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x3a, 0x0a, 0x03, 0x64, 0x69, 0x6d,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66,
	0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x69, 0x6d,
	0x52, 0x03, 0x64, 0x69, 0x6d, 0x1a, 0x2d, 0x0a, 0x03, 0x44, 0x69, 0x6d, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xef, 0x01, 0x0a, 0x0b, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x32, 0x0a, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x05, 0x64, 0x74, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x74, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70, 0x65, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x52, 0x0b, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x53, 0x68, 0x61, 0x70,
	0x65, 0x12, 0x1f, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x02, 0x42, 0x02, 0x10, 0x01, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x56,
	0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0a, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x01, 0x42, 0x02, 0x10, 0x01, 0x52, 0x09, 0x64, 0x6f, 0x75, 0x62,
	0x6c, 0x65, 0x56, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x76,
	0x61, 0x6c, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x42, 0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e,
	0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x22, 0xf2, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x65, 0x64, 0x69,
	0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x52, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x12, 0x46, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x1a,
	0x5a, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xf8, 0x01, 0x0a, 0x0f,
	0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x4a, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x6e, 0x67, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x1a, 0x5b, 0x0a, 0x0c, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e,
	0x54, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x8b, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x41, 0x54, 0x10,
	0x01, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x54, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x33, 0x32, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x54, 0x5f, 0x55, 0x49, 0x4e, 0x54, 0x38, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x44, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x31, 0x36, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x54,
	0x5f, 0x49, 0x4e, 0x54, 0x38, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x54, 0x5f, 0x53, 0x54,
	0x52, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x54, 0x5f, 0x49, 0x4e, 0x54,
	0x36, 0x34, 0x10, 0x09, 0x32, 0x67, 0x0a, 0x11, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x50, 0x72, 0x65,
	0x64, 0x69, 0x63, 0x74, 0x12, 0x22, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72, 0x65, 0x64, 0x69, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x74, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x72,
	0x65, 0x64, 0x69, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x0c, 0x5a,
	0x0a, 0x2e, 0x2f, 0x3b, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_predict_proto_rawDescOnce sync.Once
	file_predict_proto_rawDescData = file_predict_proto_rawDesc
)

func file_predict_proto_rawDescGZIP() []byte {
	file_predict_proto_rawDescOnce.Do(func() {
		file_predict_proto_rawDescData = protoimpl.X.CompressGZIP(file_predict_proto_rawDescData)
	})
	return file_predict_proto_rawDescData
}

var file_predict_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_predict_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_predict_proto_goTypes = []interface{}{
	(DataType)(0),                // 0: tensorflow.serving.DataType
	(*ModelSpec)(nil),            // 1: tensorflow.serving.ModelSpec
	(*TensorShapeProto)(nil),     // 2: tensorflow.serving.TensorShapeProto
	(*TensorProto)(nil),          // 3: tensorflow.serving.TensorProto
	(*PredictRequest)(nil),       // 4: tensorflow.serving.PredictRequest
	(*PredictResponse)(nil),      // 5: tensorflow.serving.PredictResponse
	(*TensorShapeProto_Dim)(nil), // 6: tensorflow.serving.TensorShapeProto.Dim
	nil,                          // 7: tensorflow.serving.PredictRequest.InputsEntry
	nil,                          // 8: tensorflow.serving.PredictResponse.OutputsEntry
}
var file_predict_proto_depIdxs = []int32{
	6,  // 0: tensorflow.serving.TensorShapeProto.dim:type_name -> tensorflow.serving.TensorShapeProto.Dim
	0,  // 1: tensorflow.serving.TensorProto.dtype:type_name -> tensorflow.serving.DataType
	2,  // 2: tensorflow.serving.TensorProto.tensor_shape:type_name -> tensorflow.serving.TensorShapeProto
	1,  // 3: tensorflow.serving.PredictRequest.model_spec:type_name -> tensorflow.serving.ModelSpec
	7,  // 4: tensorflow.serving.PredictRequest.inputs:type_name -> tensorflow.serving.PredictRequest.InputsEntry
	8,  // 5: tensorflow.serving.PredictResponse.outputs:type_name -> tensorflow.serving.PredictResponse.OutputsEntry
	1,  // 6: tensorflow.serving.PredictResponse.model_spec:type_name -> tensorflow.serving.ModelSpec
	3,  // 7: tensorflow.serving.PredictRequest.InputsEntry.value:type_name -> tensorflow.serving.TensorProto
	3,  // 8: tensorflow.serving.PredictResponse.OutputsEntry.value:type_name -> tensorflow.serving.TensorProto
	4,  // 9: tensorflow.serving.PredictionService.Predict:input_type -> tensorflow.serving.PredictRequest
	5,  // 10: tensorflow.serving.PredictionService.Predict:output_type -> tensorflow.serving.PredictResponse
	10, // [10:11] is the sub-list for method output_type
	9,  // [9:10] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_predict_proto_init() }
func file_predict_proto_init() {
	if File_predict_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_predict_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModelSpec); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_predict_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorShapeProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_predict_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorProto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_predict_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_predict_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PredictResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_predict_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TensorShapeProto_Dim); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_predict_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_predict_proto_goTypes,
		DependencyIndexes: file_predict_proto_depIdxs,
		EnumInfos:         file_predict_proto_enumTypes,
		MessageInfos:      file_predict_proto_msgTypes,
	}.Build()
	File_predict_proto = out.File
	file_predict_proto_rawDesc = nil
	file_predict_proto_goTypes = nil
	file_predict_proto_depIdxs = nil
}
