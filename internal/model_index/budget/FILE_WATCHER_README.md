# 文件监控功能说明

## 概述

本模块已升级支持文件监控功能，当统计文件发生变化时会自动重新加载数据，避免了定时加载导致的数据更新不及时问题。

## 功能特性

### 1. 实时文件监控
- 使用 `fsnotify` 库监控统计文件所在目录
- 当文件发生 `Write` 或 `Create` 事件时自动重新加载
- 支持文件重命名、移动等操作的监控

### 2. 智能事件处理
- 只监控指定的统计文件，忽略其他文件的变化
- 延迟100毫秒后加载，确保文件写入完成
- 详细的日志记录，便于调试和监控

### 3. 双重保障机制
- **主要方式**: 文件监控实时加载
- **备用方式**: 定时加载（可配置间隔）
- 确保在文件监控失效时仍能正常工作

### 4. 优雅关闭
- 支持优雅停止文件监控
- 自动清理资源，避免内存泄漏
- 线程安全的停止机制

## 配置说明

在 `conf/rtb_model_server.yaml` 中配置：

```yaml
BudgetConfig:
  StatsFilePath: "data/budget/stats.data"  # 统计文件路径
  StatsReloadInterval: 300                  # 备用定时重载间隔(秒)
  # ... 其他配置
```

## 使用方法

### 1. 基本使用

```go
// 获取预算处理器（自动初始化文件监控）
processor := budget.GetBudgetProcessor()

// 启动自动重载（文件监控 + 定时备用）
processor.StartAutoReload()

// 程序结束时停止监控
defer processor.StopAutoReload()
```

### 2. 手动控制

```go
// 获取统计处理器
statsProcessor := budget.GetStatsProcessor()

// 手动停止文件监控
statsProcessor.StopFileWatcher()

// 手动加载统计数据
err := statsProcessor.LoadStats()
if err != nil {
    log.Printf("加载统计数据失败: %v", err)
}
```

## 监控日志

文件监控会产生以下类型的日志：

### 初始化日志
```
file watcher initialized {"stats_file": "data/budget/stats.data", "watch_dir": "data/budget"}
```

### 文件变化日志
```
stats file changed, reloading {"file": "data/budget/stats.data", "operation": "WRITE"}
stats reloaded successfully after file change
```

### 错误日志
```
failed to reload stats after file change {"error": "..."}
file watcher error {"error": "..."}
```

### 停止日志
```
file watcher stopped
file watcher closed
```

## 性能优化

### 1. 事件去重
- 只处理目标文件的变化事件
- 忽略临时文件和其他无关文件

### 2. 延迟加载
- 文件变化后延迟100ms再加载
- 避免文件写入过程中的读取错误

### 3. 异步处理
- 文件监控在独立协程中运行
- 不阻塞主业务逻辑

## 故障处理

### 1. 文件不存在
- 初始化时如果文件不存在，会记录警告日志
- 文件创建后会自动开始监控

### 2. 权限问题
- 如果没有目录读取权限，会记录错误日志
- 系统会回退到定时加载模式

### 3. 文件系统错误
- 监控过程中的错误会记录到日志
- 不会影响现有的统计数据

## 测试和调试

### 运行演示程序

```bash
# 进入项目根目录
cd /path/to/rtb_model_server_go

# 运行文件监控演示
go run examples/file_watcher_demo.go
```

### 测试步骤

1. 启动演示程序
2. 修改 `test_stats.data` 文件内容
3. 观察控制台输出的重载日志
4. 按 Ctrl+C 退出程序

### 手动测试

```bash
# 修改统计文件
echo "1005,500,50,1,10000,1000,30" >> test_stats.data

# 观察日志输出
# 应该看到文件变化和重载成功的日志
```

## 最佳实践

### 1. 文件更新
- 使用原子操作更新文件（先写临时文件，再重命名）
- 避免直接修改正在使用的统计文件

### 2. 错误处理
- 监控文件加载的错误日志
- 设置合适的备用定时重载间隔

### 3. 性能监控
- 监控文件变化频率
- 避免过于频繁的文件更新

## 兼容性说明

- 保持了原有的 API 接口不变
- 定时加载功能仍然可用作备用方案
- 支持动态开启/关闭文件监控

## 依赖库

- `github.com/fsnotify/fsnotify`: 文件系统监控库
- 已在 `go.mod` 中声明依赖

## 注意事项

1. **文件路径**: 确保配置的统计文件路径正确
2. **权限**: 确保程序对统计文件目录有读取权限
3. **资源清理**: 程序退出时调用 `StopAutoReload()` 清理资源
4. **日志级别**: 建议设置适当的日志级别以观察监控状态