package budget

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

// 统计处理器单例
type StatsProcessor struct {
	statsMap        map[int32]*BudgetStats // 统计信息映射
	strategyStatsMap map[int32]*Stats       // 策略层级统计映射
	campaignStatsMap map[int32]*Stats       // 活动层级统计映射
	mu              sync.RWMutex           // 读写锁
	lastLoadTime    time.Time              // 最后加载时间
	watcher         *fsnotify.Watcher      // 文件监控器
	stopChan        chan struct{}          // 停止信号通道
}

var (
	statsProcessorInstance *StatsProcessor
	statsProcessorOnce     sync.Once
)

// 获取统计处理器单例
func GetStatsProcessor() *StatsProcessor {
	statsProcessorOnce.Do(func() {
		statsProcessorInstance = &StatsProcessor{
			statsMap:        make(map[int32]*BudgetStats),
			strategyStatsMap: make(map[int32]*Stats),
			campaignStatsMap: make(map[int32]*Stats),
			lastLoadTime:    time.Now(),
			stopChan:        make(chan struct{}),
		}
		// 初始化文件监控器
		if err := statsProcessorInstance.initFileWatcher(); err != nil {
			zaplog.Logger.Error("failed to initialize file watcher", zap.Error(err))
		}
	})
	return statsProcessorInstance
}

// 加载统计数据
func (s *StatsProcessor) LoadStats() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	statsFilePath := conf.GlobalConfig.BudgetConfig.StatsFilePath
	file, err := os.Open(statsFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			zaplog.Logger.Error("stats file not exist, skip loading", zap.String("path", statsFilePath), zap.Error(err))
			return nil
		}
		return fmt.Errorf("failed to open stats file: %v", err)
	}
	defer file.Close()

	newStatsMap := make(map[int32]*BudgetStats)
	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		stats, err := s.parseStatsLine(line)
		if err != nil {
			zaplog.Logger.Warn("failed to parse stats line",
				zap.Int("line_num", lineNum),
				zap.String("line", line),
				zap.Error(err))
			continue
		}

		newStatsMap[stats.CreativeId] = stats
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading stats file: %v", err)
	}

	s.statsMap = newStatsMap
	s.lastLoadTime = time.Now()

	zaplog.Logger.Info("stats loaded successfully",
		zap.String("path", statsFilePath),
		zap.Int("count", len(newStatsMap)))

	return nil
}

// 解析统计数据行
func (s *StatsProcessor) parseStatsLine(line string) (*BudgetStats, error) {
	fields := strings.Split(line, ",")
	if len(fields) != 7 {
		return nil, fmt.Errorf("invalid stats line format, expected 7 fields but got %d", len(fields))
	}

	stats := &BudgetStats{}

	// 解析创意ID
	if creativeId, err := strconv.ParseInt(fields[0], 10, 32); err == nil {
		stats.CreativeId = int32(creativeId)
	} else {
		return nil, fmt.Errorf("invalid creative_id: %s", fields[0])
	}

	// 解析策略ID
	if strategyId, err := strconv.ParseInt(fields[1], 10, 32); err == nil {
		stats.StrategyId = int32(strategyId)
	} else {
		return nil, fmt.Errorf("invalid strategy_id: %s", fields[1])
	}

	// 解析活动ID
	if campaignId, err := strconv.ParseInt(fields[2], 10, 32); err == nil {
		stats.CampaignId = int32(campaignId)
	} else {
		return nil, fmt.Errorf("invalid campaign_id: %s", fields[2])
	}

	// 解析交易所ID
	if exchangeId, err := strconv.ParseInt(fields[3], 10, 32); err == nil {
		stats.ExchangeId = int32(exchangeId)
	} else {
		return nil, fmt.Errorf("invalid exchange_id: %s", fields[3])
	}

	// 解析消费金额
	if cost, err := strconv.ParseInt(fields[4], 10, 64); err == nil {
		stats.Cost = cost
	} else {
		return nil, fmt.Errorf("invalid cost: %s", fields[4])
	}

	// 解析展示次数
	if impressions, err := strconv.ParseInt(fields[5], 10, 64); err == nil {
		stats.Impressions = impressions
	} else {
		return nil, fmt.Errorf("invalid impressions: %s", fields[5])
	}

	// 解析点击次数
	if clicks, err := strconv.ParseInt(fields[6], 10, 64); err == nil {
		stats.Clicks = clicks
	} else {
		return nil, fmt.Errorf("invalid clicks: %s", fields[6])
	}

	stats.LastUpdateTime = time.Now().Unix()
	
	// 同时计算策略层级统计
	if strategyStats, exists := s.strategyStatsMap[stats.StrategyId]; exists {
		strategyStats.Cost += stats.Cost
		strategyStats.Impressions += stats.Impressions
		strategyStats.Clicks += stats.Clicks
		stats.StrategyStats = strategyStats
	} else {
		strategyStats := &Stats{
			Cost:        stats.Cost,
			Impressions: stats.Impressions,
			Clicks:      stats.Clicks,
		}
		s.strategyStatsMap[stats.StrategyId] = strategyStats
		stats.StrategyStats = strategyStats
	}
	
	// 同时计算活动层级统计
	if campaignStats, exists := s.campaignStatsMap[stats.CampaignId]; exists {
		campaignStats.Cost += stats.Cost
		campaignStats.Impressions += stats.Impressions
		campaignStats.Clicks += stats.Clicks
		stats.CampaignStats = campaignStats
	} else {
		campaignStats := &Stats{
			Cost:        stats.Cost,
			Impressions: stats.Impressions,
			Clicks:      stats.Clicks,
		}
		s.campaignStatsMap[stats.CampaignId] = campaignStats
		stats.CampaignStats = campaignStats
	}
	
	return stats, nil
}

// 获取统计信息
func (s *StatsProcessor) GetStats(creativeId int32) *BudgetStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.statsMap[creativeId]; exists {
		return stats
	}
	return nil
}

// 获取策略层级统计信息
func (s *StatsProcessor) GetStrategyStats(strategyId int32) *Stats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.strategyStatsMap[strategyId]; exists {
		return stats
	}
	return nil
}

// 获取活动层级统计信息
func (s *StatsProcessor) GetCampaignStats(campaignId int32) *Stats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.campaignStatsMap[campaignId]; exists {
		return stats
	}
	return nil
}

// 更新统计信息
func (s *StatsProcessor) UpdateStats(creativeId int32, cost int64, impressions int64, clicks int64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if stats, exists := s.statsMap[creativeId]; exists {
		stats.Cost += cost
		stats.Impressions += impressions
		stats.Clicks += clicks
		stats.LastUpdateTime = time.Now().Unix()
		
		// 同时更新策略层级统计
		if strategyStats, exists := s.strategyStatsMap[stats.StrategyId]; exists {
			strategyStats.Cost += cost
			strategyStats.Impressions += impressions
			strategyStats.Clicks += clicks
		}
		
		// 同时更新活动层级统计
		if campaignStats, exists := s.campaignStatsMap[stats.CampaignId]; exists {
			campaignStats.Cost += cost
			campaignStats.Impressions += impressions
			campaignStats.Clicks += clicks
		}
	} else {
		// 如果不存在，创建新的统计记录
		s.statsMap[creativeId] = &BudgetStats{
			CreativeId:     creativeId,
			Cost:           cost,
			Impressions:    impressions,
			Clicks:         clicks,
			LastUpdateTime: time.Now().Unix(),
		}
	}
}

// 获取所有统计信息
func (s *StatsProcessor) GetAllStats() map[int32]*BudgetStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 返回副本以避免并发问题
	result := make(map[int32]*BudgetStats)
	for k, v := range s.statsMap {
		result[k] = &BudgetStats{
			CreativeId:     v.CreativeId,
			StrategyId:     v.StrategyId,
			CampaignId:     v.CampaignId,
			ExchangeId:     v.ExchangeId,
			Cost:           v.Cost,
			Impressions:    v.Impressions,
			Clicks:         v.Clicks,
			LastUpdateTime: v.LastUpdateTime,
		}
	}
	return result
}

// 获取统计信息摘要
func (s *StatsProcessor) GetStatsSummary() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	summary := make(map[string]interface{})
	summary["total_creatives"] = len(s.statsMap)
	summary["last_load_time"] = s.lastLoadTime.Format("2006-01-02 15:04:05")

	// 统计各交易所的创意数量
	exchangeStats := make(map[int32]int)
	for _, stats := range s.statsMap {
		exchangeStats[stats.ExchangeId]++
	}
	summary["exchange_stats"] = exchangeStats

	return summary
}

// 初始化文件监控器
func (s *StatsProcessor) initFileWatcher() error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create file watcher: %v", err)
	}
	s.watcher = watcher

	statsFilePath := conf.GlobalConfig.BudgetConfig.StatsFilePath
	if statsFilePath == "" {
		zaplog.Logger.Warn("stats file path is empty, file watching disabled")
		return nil
	}

	// 监控文件所在的目录
	statsDir := filepath.Dir(statsFilePath)
	if err := s.watcher.Add(statsDir); err != nil {
		return fmt.Errorf("failed to watch directory %s: %v", statsDir, err)
	}

	// 启动文件监控协程
	go s.watchFileChanges()

	zaplog.Logger.Info("file watcher initialized", 
		zap.String("stats_file", statsFilePath),
		zap.String("watch_dir", statsDir))

	return nil
}

// 监控文件变化
func (s *StatsProcessor) watchFileChanges() {
	statsFilePath := conf.GlobalConfig.BudgetConfig.StatsFilePath
	statsFileName := filepath.Base(statsFilePath)

	for {
		select {
		case event, ok := <-s.watcher.Events:
			if !ok {
				return
			}

			// 检查是否是我们关心的文件
			if filepath.Base(event.Name) != statsFileName {
				continue
			}

			// 处理文件写入和创建事件
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				zaplog.Logger.Info("stats file changed, reloading", 
					zap.String("file", event.Name),
					zap.String("operation", event.Op.String()))

				// 延迟一小段时间，确保文件写入完成
				time.Sleep(100 * time.Millisecond)

				if err := s.LoadStats(); err != nil {
					zaplog.Logger.Error("failed to reload stats after file change", zap.Error(err))
				} else {
					zaplog.Logger.Info("stats reloaded successfully after file change")
				}
			}

		case err, ok := <-s.watcher.Errors:
			if !ok {
				return
			}
			zaplog.Logger.Error("file watcher error", zap.Error(err))

		case <-s.stopChan:
			zaplog.Logger.Info("file watcher stopped")
			return
		}
	}
}

// 停止文件监控
func (s *StatsProcessor) StopFileWatcher() {
	if s.watcher != nil {
		close(s.stopChan)
		s.watcher.Close()
		zaplog.Logger.Info("file watcher closed")
	}
}

// 定期重载统计数据（保留作为备用方案）
func (s *StatsProcessor) StartAutoReload() {
	interval := time.Duration(conf.GlobalConfig.BudgetConfig.StatsReloadInterval) * time.Second
	ticker := time.NewTicker(interval)

	go func() {
		for range ticker.C {
			select {
			case <-s.stopChan:
				ticker.Stop()
				return
			default:
				if err := s.LoadStats(); err != nil {
					zaplog.Logger.Error("failed to reload stats", zap.Error(err))
				} else {
					zaplog.Logger.Info("stats reloaded successfully (periodic)")
				}
			}
		}
	}()
}
